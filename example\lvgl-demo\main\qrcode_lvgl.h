/**
 * @file qrcode_lvgl.h
 * @brief LVGL QR码显示功能头文件
 * <AUTHOR> Agent
 * @date 2025-08-01
 * 
 * 本文件包含LVGL特定的QR码显示函数声明
 * 将LVGL相关功能从qrcodegen.h中分离，保持qrcodegen库的独立性
 */

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// 包含必要的头文件
#include "lvgl.h"
#include "qrcodegen.h"

/*---- LVGL QR Code Display Functions ----*/

/**
 * @brief 在LVGL画布上绘制二维码（高质量版本）
 * @param parent 父对象，二维码将作为其子对象创建
 * @param text 要编码的文本内容
 * @param x_pos X坐标位置
 * @param y_pos Y坐标位置
 * @param scale 缩放比例（每个模块的像素大小）
 * @return 成功返回画布对象指针，失败返回NULL
 * @note 此函数使用LVGL画布，内存占用较大但显示质量高
 */
lv_obj_t* qrcode_create_canvas(lv_obj_t* parent, const char* text, lv_coord_t x_pos, lv_coord_t y_pos, int scale);

/**
 * @brief 简化版本的QR码显示函数，使用矩形对象
 * @param parent 父对象
 * @param text 要编码的文本
 * @param x_pos X坐标
 * @param y_pos Y坐标
 * @param scale 缩放比例
 * @return 容器对象指针或NULL
 * @note 此函数使用LVGL矩形对象，内存占用小，推荐使用
 */
lv_obj_t* qrcode_create_simple(lv_obj_t* parent, const char* text, lv_coord_t x_pos, lv_coord_t y_pos, int scale);

/**
 * @brief 生成并显示支付宝二维码（画布版本）
 * @param parent 父对象
 * @return 成功返回画布对象指针，失败返回NULL
 * @note 高质量版本，内存占用较大
 */
lv_obj_t* qrcode_show_alipay(lv_obj_t* parent);

/**
 * @brief 生成并显示支付宝二维码（简化版本）
 * @param parent 父对象
 * @return 容器对象指针或NULL
 * @note 内存友好版本，推荐使用
 */
lv_obj_t* qrcode_show_alipay_simple(lv_obj_t* parent);

/**
 * @brief 测试QR码生成功能
 * @note 在控制台输出测试QR码，用于调试
 */
void qrcode_test_generation(void);

/**
 * @brief 释放QR码画布资源
 * @param canvas 画布对象
 * @note 仅用于画布版本的资源清理
 */
void qrcode_canvas_cleanup(lv_obj_t* canvas);

/*---- 配置常量 ----*/

// 显示屏参数 (基于ZX2D80CE02S-2432配置)
#define QRCODE_SCREEN_WIDTH   240
#define QRCODE_SCREEN_HEIGHT  320

// 二维码显示参数
#define QRCODE_BORDER_SIZE    4    // 二维码边框大小
#define QRCODE_MAX_SIZE      200   // 二维码最大显示尺寸
#define QRCODE_MIN_SCALE      2    // 最小缩放比例
#define QRCODE_MAX_SCALE      8    // 最大缩放比例

// 支付宝二维码URL
#define QRCODE_ALIPAY_URL "https://render.alipay.hk/p/s/hkwallet/landing/qrcode?code=281004040598XXwl31cYpzUaA50b9ga0dFkQ"

/*---- 辅助宏 ----*/

/**
 * @brief 计算居中位置的X坐标
 * @param qr_size QR码显示尺寸
 */
#define QRCODE_CENTER_X(qr_size) ((QRCODE_SCREEN_WIDTH - (qr_size)) / 2)

/**
 * @brief 计算居中位置的Y坐标
 * @param qr_size QR码显示尺寸
 */
#define QRCODE_CENTER_Y(qr_size) ((QRCODE_SCREEN_HEIGHT - (qr_size)) / 2)

/**
 * @brief 检查坐标是否在屏幕范围内
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 */
#define QRCODE_IN_BOUNDS(x, y, w, h) \
    ((x) >= 0 && (y) >= 0 && \
     ((x) + (w)) <= QRCODE_SCREEN_WIDTH && \
     ((y) + (h)) <= QRCODE_SCREEN_HEIGHT)

#ifdef __cplusplus
}
#endif
