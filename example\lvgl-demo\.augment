# Augment 配置文件 - ESP32-S3 LVGL 项目
# 项目：嵌入式 GUI 应用开发
# 硬件：ESP32-S3 微控制器 + ZX2D80CE02S-2432 显示屏 (240x320)
# UI框架：LVGL 8.3.1
# 开发环境：ESP-IDF

# ============================================================================
# 项目上下文信息
# ============================================================================
[project]
name = "ESP32-S3 LVGL Demo"
description = "基于ESP32-S3微控制器的LVGL GUI应用开发项目"
target_hardware = "ESP32-S3"
display_resolution = "240x320"
ui_framework = "LVGL 8.3.1"
build_system = "ESP-IDF + CMake"
primary_purpose = "嵌入式GUI应用开发"

# ============================================================================
# 包含的分析目录（只读模式）
# ============================================================================
[analysis.readonly_paths]
# 板级支持包组件
board_utility = "../../components/qmsd_board/board_utility"
board_specific = "../../components/qmsd_board/board/esp32-s3/ZX2D80CE02S-2432"
gui_components = "../../components/qmsd_gui"
screen_drivers = "../../components/qmsd_screen"
touch_drivers = "../../components/qmsd_touch"
utilities = "../../components/qmsd_utils"

# 扩展组件
extensions = "../../components-ext"

# 第三方组件
third_party = "../../components-third-party"

# 工具和脚本
tools = "../../tools"

# ============================================================================
# 代码分析规则
# ============================================================================
[analysis.rules]
# C/C++ 特定规则
c_standard = "C11"
cpp_standard = "C++17"

# ESP-IDF 特定配置
esp_idf_version = "v5.x"
target_chip = "esp32s3"

# LVGL 特定配置
lvgl_version = "8.3.1"
lvgl_color_depth = "16"
lvgl_dpi = "130"

# 硬件约束
max_memory_usage = "8MB"  # ESP32-S3 PSRAM
flash_size = "16MB"
cpu_frequency = "240MHz"

# ============================================================================
# 文件类型和模式
# ============================================================================
[analysis.file_patterns]
# 包含的文件类型
include = [
    "*.c",
    "*.h",
    "*.cpp",
    "*.hpp",
    "*.cmake",
    "CMakeLists.txt",
    "Kconfig*",
    "sdkconfig*",
    "*.py",
    "*.md"
]

# 排除的文件和目录
exclude = [
    "build/",
    "*.bin",
    "*.elf",
    "*.map",
    "*.o",
    "*.a",
    ".git/",
    "__pycache__/",
    "*.pyc"
]

# ============================================================================
# 代码建议和约束
# ============================================================================
[suggestions]
# 内存管理
memory_management = [
    "优先使用ESP-IDF的内存管理函数（heap_caps_malloc等）",
    "注意PSRAM和内部RAM的使用分配",
    "避免在中断服务程序中使用动态内存分配",
    "使用LVGL的内存池管理机制"
]

# 性能优化
performance = [
    "使用DMA传输减少CPU负载",
    "合理配置LVGL刷新率和缓冲区大小",
    "避免在GUI任务中执行耗时操作",
    "使用FreeRTOS任务优先级合理分配CPU时间"
]

# 硬件相关
hardware_constraints = [
    "GPIO配置需要考虑ESP32-S3的多路复用限制",
    "SPI总线速度不要超过硬件规格",
    "注意电源管理和低功耗模式",
    "触摸屏校准参数需要根据实际硬件调整"
]

# LVGL最佳实践
lvgl_best_practices = [
    "使用lv_obj_create_style_transition创建平滑动画",
    "合理使用lv_group管理焦点和输入",
    "避免在lv_timer_handler中执行耗时操作",
    "使用lv_mem_monitor监控内存使用情况"
]

# ============================================================================
# 开发环境配置
# ============================================================================
[development]
# 编译器标志
compiler_flags = [
    "-Wall",
    "-Wextra",
    "-Werror=return-type",
    "-Wno-unused-parameter",
    "-DLVGL_VERSION_8_3_1"
]

# 调试配置
debug_settings = [
    "CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT=y",
    "CONFIG_ESP_DEBUG_OCDAWARE=y",
    "CONFIG_FREERTOS_DEBUG_OCDAWARE=y"
]

# 优化级别
optimization = "CONFIG_COMPILER_OPTIMIZATION_SIZE"

# ============================================================================
# 特定组件配置
# ============================================================================
[components]
# QMSD板级组件
[components.qmsd_board]
description = "板级支持包，包含硬件抽象层"
key_files = ["qmsd_board.h", "qmsd_board_def.h", "qmsd_board_pin.h"]

[components.qmsd_screen]
description = "显示屏驱动组件"
resolution = "240x320"
color_depth = "16bit"
interface = "SPI"

[components.qmsd_touch]
description = "触摸屏驱动组件"
interface = "I2C"
calibration_required = true

[components.qmsd_gui]
description = "GUI相关工具和封装"
framework = "LVGL"

# LVGL配置
[components.lvgl]
version = "8.3.1"
color_format = "RGB565"
buffer_size = "1/4 screen"
anti_aliasing = true
animation_support = true

# ============================================================================
# 代码质量检查
# ============================================================================
[quality]
# 静态分析
static_analysis = true
memory_leak_detection = true
stack_usage_analysis = true

# 代码风格
coding_style = "ESP-IDF"
max_line_length = 120
indent_style = "spaces"
indent_size = 4

# 命名约定
naming_conventions = [
    "函数名使用snake_case",
    "宏定义使用UPPER_CASE",
    "结构体使用snake_case_t后缀",
    "全局变量使用g_前缀"
]

# ============================================================================
# 文档和注释
# ============================================================================
[documentation]
# 必需的文档
required_docs = [
    "函数参数和返回值说明",
    "复杂算法的实现原理",
    "硬件相关配置的说明",
    "API使用示例"
]

# 注释风格
comment_style = "Doxygen"
header_template = true

# ============================================================================
# 测试配置
# ============================================================================
[testing]
# 单元测试框架
unit_test_framework = "Unity"
test_directory = "test/"

# 硬件在环测试
hardware_testing = true
simulation_testing = false

# 覆盖率要求
code_coverage_target = "80%"
