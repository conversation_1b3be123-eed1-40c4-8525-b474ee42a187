/**
 * @file qrcode_simple.c
 * @brief 最简化的QR码显示实现，确保编译通过
 * <AUTHOR> Agent
 * @date 2025-08-01
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "qrcode_lvgl.h"

static const char *TAG = "QRCODE_SIMPLE";

/**
 * @brief 测试QR码生成功能（仅控制台输出）
 */
void qrcode_test_generation(void)
{
    ESP_LOGI(TAG, "Testing QR code generation...");
    
    const char* test_text = "Hello World";
    
    // 分配缓冲区
    uint8_t *qrcode = malloc(qrcodegen_BUFFER_LEN_MAX);
    uint8_t *temp_buffer = malloc(qrcodegen_BUFFER_LEN_MAX);
    
    if (!qrcode || !temp_buffer) {
        ESP_LOGE(TAG, "Failed to allocate test buffers");
        if (qrcode) free(qrcode);
        if (temp_buffer) free(temp_buffer);
        return;
    }
    
    memset(qrcode, 0, qrcodegen_BUFFER_LEN_MAX);
    memset(temp_buffer, 0, qrcodegen_BUFFER_LEN_MAX);
    
    // 生成测试二维码
    bool success = qrcodegen_encodeText(
        test_text,
        temp_buffer,
        qrcode,
        qrcodegen_Ecc_LOW,
        qrcodegen_VERSION_MIN,
        qrcodegen_VERSION_MAX,
        qrcodegen_Mask_AUTO,
        true
    );
    
    if (success) {
        int size = qrcodegen_getSize(qrcode);
        ESP_LOGI(TAG, "Test QR code generated successfully, size: %d x %d", size, size);
        
        // 简单的控制台输出（用于调试）
        printf("\nQR Code for '%s' (size: %d):\n", test_text, size);
        for (int y = -1; y <= size; y++) {
            for (int x = -1; x <= size; x++) {
                if (qrcodegen_getModule(qrcode, x, y)) {
                    printf("##");
                } else {
                    printf("  ");
                }
            }
            printf("\n");
        }
        printf("\n");
    } else {
        ESP_LOGE(TAG, "Failed to generate test QR code");
    }
    
    free(qrcode);
    free(temp_buffer);
}

/**
 * @brief 简化版本的QR码显示函数
 * @param parent 父对象
 * @param text 要编码的文本
 * @param x_pos X坐标
 * @param y_pos Y坐标
 * @param scale 缩放比例
 * @return 容器对象指针或NULL
 */
lv_obj_t* qrcode_create_simple(lv_obj_t* parent, const char* text, lv_coord_t x_pos, lv_coord_t y_pos, int scale)
{
    if (!parent || !text || strlen(text) == 0) {
        ESP_LOGE(TAG, "Invalid parameters");
        return NULL;
    }
    
    ESP_LOGI(TAG, "Creating simple QR code for: %.30s%s", text, strlen(text) > 30 ? "..." : "");
    
    // 分配QR码生成所需的缓冲区
    uint8_t *qrcode = malloc(qrcodegen_BUFFER_LEN_MAX);
    uint8_t *temp_buffer = malloc(qrcodegen_BUFFER_LEN_MAX);
    
    if (!qrcode || !temp_buffer) {
        ESP_LOGE(TAG, "Failed to allocate memory for QR code generation");
        if (qrcode) free(qrcode);
        if (temp_buffer) free(temp_buffer);
        return NULL;
    }
    
    // 清零缓冲区
    memset(qrcode, 0, qrcodegen_BUFFER_LEN_MAX);
    memset(temp_buffer, 0, qrcodegen_BUFFER_LEN_MAX);
    
    // 生成二维码
    bool success = qrcodegen_encodeText(
        text,
        temp_buffer,
        qrcode,
        qrcodegen_Ecc_LOW,           // 低错误纠正级别
        qrcodegen_VERSION_MIN,       // 最小版本
        qrcodegen_VERSION_MAX,       // 最大版本
        qrcodegen_Mask_AUTO,         // 自动选择掩码
        true                         // 提升错误纠正级别
    );
    
    if (!success) {
        ESP_LOGE(TAG, "Failed to generate QR code");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }
    
    // 获取二维码尺寸
    int qr_size = qrcodegen_getSize(qrcode);
    ESP_LOGI(TAG, "Generated QR code size: %d x %d", qr_size, qr_size);
    
    // 如果没有指定缩放比例，自动计算
    if (scale <= 0) {
        scale = 180 / qr_size;  // 目标尺寸约180像素
        if (scale < 2) scale = 2;
        if (scale > 5) scale = 5;
    }
    
    // 计算总尺寸
    int border = 2;  // 边框大小
    int total_size = (qr_size + border * 2) * scale;
    
    ESP_LOGI(TAG, "QR code scale: %d, total size: %d x %d", scale, total_size, total_size);
    
    // 创建容器
    lv_obj_t *container = lv_obj_create(parent);
    if (!container) {
        ESP_LOGE(TAG, "Failed to create container");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }
    
    lv_obj_set_size(container, total_size, total_size);
    lv_obj_set_pos(container, x_pos, y_pos);
    lv_obj_set_style_bg_color(container, lv_color_white(), 0);
    lv_obj_set_style_border_width(container, 0, 0);
    lv_obj_set_style_pad_all(container, 0, 0);
    lv_obj_clear_flag(container, LV_OBJ_FLAG_SCROLLABLE);
    
    // 绘制二维码模块
    int modules_created = 0;
    for (int y = 0; y < qr_size; y++) {
        for (int x = 0; x < qr_size; x++) {
            if (qrcodegen_getModule(qrcode, x, y)) {
                // 创建黑色矩形表示暗模块
                lv_obj_t *module = lv_obj_create(container);
                if (module) {
                    lv_obj_set_size(module, scale, scale);
                    lv_obj_set_pos(module, (x + border) * scale, (y + border) * scale);
                    lv_obj_set_style_bg_color(module, lv_color_black(), 0);
                    lv_obj_set_style_border_width(module, 0, 0);
                    lv_obj_set_style_radius(module, 0, 0);
                    lv_obj_clear_flag(module, LV_OBJ_FLAG_CLICKABLE);
                    lv_obj_clear_flag(module, LV_OBJ_FLAG_SCROLLABLE);
                    modules_created++;
                }
            }
        }
    }
    
    // 释放临时缓冲区
    free(qrcode);
    free(temp_buffer);
    
    ESP_LOGI(TAG, "Simple QR code created successfully at (%d, %d), modules: %d", 
             x_pos, y_pos, modules_created);
    
    return container;
}

/**
 * @brief 生成并显示支付宝二维码（简化版本）
 * @param parent 父对象
 * @return 容器对象指针或NULL
 */
lv_obj_t* qrcode_show_alipay_simple(lv_obj_t* parent)
{
    if (!parent) {
        ESP_LOGE(TAG, "Parent object is NULL");
        return NULL;
    }
    
    // 计算居中位置
    lv_coord_t x_pos = QRCODE_CENTER_X(180);  // QR码约180像素
    lv_coord_t y_pos = QRCODE_CENTER_Y(180);

    ESP_LOGI(TAG, "Creating simple Alipay QR code at center position (%d, %d)", x_pos, y_pos);

    return qrcode_create_simple(parent, QRCODE_ALIPAY_URL, x_pos, y_pos, 0);
}
