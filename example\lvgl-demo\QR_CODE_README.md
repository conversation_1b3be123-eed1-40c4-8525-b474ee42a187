# ESP32-S3 LVGL 二维码显示功能

## 概述

本项目为ESP32-S3 + LVGL项目添加了二维码生成和显示功能，支持在240x320分辨率的显示屏上绘制二维码。

## 功能特性

- ✅ 基于qrcodegen库生成高质量二维码
- ✅ 适配LVGL 8.3.1显示系统
- ✅ 支持240x320分辨率显示屏
- ✅ 自动计算最佳缩放比例
- ✅ 内存优化，支持PSRAM和内部RAM
- ✅ 支持长URL编码（如支付宝二维码）
- ✅ 提供两种实现方式：画布版本和简化版本

## 文件结构

```
main/
├── qrcodegen.h          # QR码生成库头文件（已扩展）
├── qrcodegen.c          # QR码生成库实现
├── qrcode_display.c     # LVGL画布版本实现
├── qrcode_test.c        # 简化版本实现和测试
├── main.c               # 主程序（已集成QR码功能）
└── CMakeLists.txt       # 构建配置
```

## API 接口

### 画布版本（高质量，内存占用较大）

```c
/**
 * @brief 在LVGL画布上绘制二维码
 * @param parent 父对象，二维码将作为其子对象创建
 * @param text 要编码的文本内容
 * @param x_pos X坐标位置
 * @param y_pos Y坐标位置
 * @param scale 缩放比例（每个模块的像素大小）
 * @return 成功返回画布对象指针，失败返回NULL
 */
lv_obj_t* qrcode_create_canvas(lv_obj_t* parent, const char* text, 
                              lv_coord_t x_pos, lv_coord_t y_pos, int scale);

/**
 * @brief 生成并显示支付宝二维码
 * @param parent 父对象
 * @return 成功返回画布对象指针，失败返回NULL
 */
lv_obj_t* qrcode_show_alipay(lv_obj_t* parent);
```

### 简化版本（内存友好，使用矩形对象）

```c
/**
 * @brief 简化版本的QR码显示函数，使用矩形对象而不是画布
 * @param parent 父对象
 * @param text 要编码的文本
 * @param x_pos X坐标
 * @param y_pos Y坐标
 * @param scale 缩放比例
 * @return 容器对象指针或NULL
 */
lv_obj_t* qrcode_create_simple(lv_obj_t* parent, const char* text, 
                              lv_coord_t x_pos, lv_coord_t y_pos, int scale);

/**
 * @brief 生成并显示支付宝二维码（简化版本）
 * @param parent 父对象
 * @return 容器对象指针或NULL
 */
lv_obj_t* qrcode_show_alipay_simple(lv_obj_t* parent);
```

## 使用方法

### 1. 基本使用

```c
#include "qrcodegen.h"

void display_qr_code(void) {
    // 获取当前屏幕
    lv_obj_t* screen = lv_scr_act();
    
    // 显示支付宝二维码（简化版本，推荐）
    lv_obj_t* qr_obj = qrcode_show_alipay_simple(screen);
    
    if (qr_obj) {
        printf("QR code displayed successfully\n");
    } else {
        printf("Failed to display QR code\n");
    }
}
```

### 2. 自定义内容

```c
void display_custom_qr(void) {
    lv_obj_t* screen = lv_scr_act();
    
    // 显示自定义文本的二维码
    const char* custom_text = "https://www.example.com";
    lv_obj_t* qr_obj = qrcode_create_simple(screen, custom_text, 20, 60, 3);
    
    if (qr_obj) {
        printf("Custom QR code displayed\n");
    }
}
```

### 3. 集成到现有项目

在 `main.c` 中的 `gui_user_init()` 函数中添加：

```c
void gui_user_init() {
    // 原有的GUI初始化代码
    lv_demo_widgets();
    
    // 添加QR码显示按钮
    create_qr_button();
}
```

## 配置参数

### 显示屏参数
```c
#define SCREEN_WIDTH  240    // 屏幕宽度
#define SCREEN_HEIGHT 320    // 屏幕高度
```

### 二维码参数
```c
#define QR_BORDER_SIZE    4    // 二维码边框大小
#define QR_MAX_SIZE      200   // 二维码最大显示尺寸
#define QR_MIN_SCALE      2    // 最小缩放比例
#define QR_MAX_SCALE      8    // 最大缩放比例
```

## 内存使用

### 画布版本
- QR码生成缓冲区: ~3KB (临时)
- 画布缓冲区: 200×200×2 = 80KB (持久)
- 总计: ~83KB

### 简化版本
- QR码生成缓冲区: ~3KB (临时)
- LVGL对象: ~几KB (持久)
- 总计: ~5-10KB

## 性能优化

1. **内存分配策略**
   - 优先使用PSRAM存储画布缓冲区
   - 临时缓冲区使用内部RAM
   - 及时释放不需要的内存

2. **显示优化**
   - 自动计算最佳缩放比例
   - 支持边框和居中显示
   - 避免超出屏幕边界

3. **错误处理**
   - 完整的内存分配检查
   - QR码生成失败处理
   - 资源清理机制

## 故障排除

### 1. 编译错误
- 确保包含了所有必要的头文件
- 检查CMakeLists.txt配置
- 验证ESP-IDF环境设置

### 2. 内存不足
- 使用简化版本而不是画布版本
- 检查PSRAM配置
- 减小QR码尺寸

### 3. 显示问题
- 检查屏幕分辨率配置
- 验证LVGL配置
- 确认坐标计算正确

## 示例代码

完整的使用示例请参考 `main.c` 文件中的实现。项目支持两种启动模式：

1. **模式0**: 原始widgets demo + QR码按钮
2. **模式1**: 直接显示QR码测试

通过修改 `gui_user_init()` 中的 `startup_mode` 变量来切换模式。

## 技术细节

- **QR码库**: qrcodegen (支持QR Code Model 2标准)
- **错误纠正**: 低级别 (约7%容错率)
- **编码模式**: 自动选择 (数字/字母/字节)
- **版本范围**: 1-40 (自动选择最小版本)
- **颜色格式**: RGB565 (LVGL标准)

## 许可证

本代码基于原项目许可证，QR码生成库遵循MIT许可证。
