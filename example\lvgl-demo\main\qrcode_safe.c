/**
 * @file qrcode_safe.c
 * @brief 内存安全的QR码显示实现
 * <AUTHOR> Agent
 * @date 2025-08-01
 * 
 * 使用LVGL画布而不是大量小对象来避免内存碎片和对象创建失败
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "qrcode_lvgl.h"

static const char *TAG = "QRCODE_SAFE";

/**
 * @brief 使用画布创建QR码（内存安全版本）
 * @param parent 父对象
 * @param text 要编码的文本
 * @param x_pos X坐标
 * @param y_pos Y坐标
 * @param scale 缩放比例
 * @return 画布对象指针或NULL
 */
lv_obj_t* qrcode_create_canvas_safe(lv_obj_t* parent, const char* text, lv_coord_t x_pos, lv_coord_t y_pos, int scale)
{
    // 严格的参数验证
    if (!parent || !lv_obj_is_valid(parent)) {
        ESP_LOGE(TAG, "Parent object is NULL or invalid");
        return NULL;
    }
    
    if (!text || strlen(text) == 0) {
        ESP_LOGE(TAG, "Text is NULL or empty");
        return NULL;
    }
    
    // 检查可用内存
    size_t free_heap = esp_get_free_heap_size();
    ESP_LOGI(TAG, "Free heap before QR creation: %zu bytes", free_heap);
    
    if (free_heap < 100000) {  // 画布需要更多内存
        ESP_LOGE(TAG, "Insufficient memory for canvas QR code: %zu bytes", free_heap);
        return NULL;
    }
    
    ESP_LOGI(TAG, "Creating canvas QR code for: %.30s%s", text, strlen(text) > 30 ? "..." : "");
    
    // 分配QR码生成缓冲区
    uint8_t *qrcode = malloc(qrcodegen_BUFFER_LEN_MAX);
    uint8_t *temp_buffer = malloc(qrcodegen_BUFFER_LEN_MAX);
    
    if (!qrcode || !temp_buffer) {
        ESP_LOGE(TAG, "Failed to allocate QR generation buffers");
        if (qrcode) free(qrcode);
        if (temp_buffer) free(temp_buffer);
        return NULL;
    }
    
    // 清零缓冲区
    memset(qrcode, 0, qrcodegen_BUFFER_LEN_MAX);
    memset(temp_buffer, 0, qrcodegen_BUFFER_LEN_MAX);
    
    // 生成二维码
    bool success = qrcodegen_encodeText(
        text,
        temp_buffer,
        qrcode,
        qrcodegen_Ecc_LOW,
        qrcodegen_VERSION_MIN,
        qrcodegen_VERSION_MAX,
        qrcodegen_Mask_AUTO,
        true
    );
    
    if (!success) {
        ESP_LOGE(TAG, "Failed to generate QR code");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }
    
    // 获取二维码尺寸
    int qr_size = qrcodegen_getSize(qrcode);
    ESP_LOGI(TAG, "Generated QR code size: %d x %d", qr_size, qr_size);
    
    // 自动计算缩放比例
    if (scale <= 0) {
        scale = 160 / qr_size;  // 目标尺寸约160像素
        if (scale < 2) scale = 2;
        if (scale > 4) scale = 4;  // 限制最大缩放，节省内存
    }
    
    // 计算画布尺寸
    int border = 2;
    int canvas_size = (qr_size + border * 2) * scale;
    
    ESP_LOGI(TAG, "Canvas size: %d x %d, scale: %d", canvas_size, canvas_size, scale);
    
    // 创建画布对象
    lv_obj_t *canvas = lv_canvas_create(parent);
    if (!canvas || !lv_obj_is_valid(canvas)) {
        ESP_LOGE(TAG, "Failed to create canvas object");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }
    
    // 计算画布缓冲区大小
    size_t canvas_buf_size = canvas_size * canvas_size * sizeof(lv_color_t);
    ESP_LOGI(TAG, "Canvas buffer size: %zu bytes", canvas_buf_size);
    
    // 分配画布缓冲区（优先使用PSRAM）
    lv_color_t *canvas_buf = NULL;
    
    // 首先尝试PSRAM
    canvas_buf = heap_caps_malloc(canvas_buf_size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (canvas_buf) {
        ESP_LOGI(TAG, "Using PSRAM for canvas buffer");
    } else {
        // PSRAM失败，尝试内部RAM
        canvas_buf = heap_caps_malloc(canvas_buf_size, MALLOC_CAP_8BIT);
        if (canvas_buf) {
            ESP_LOGW(TAG, "Using internal RAM for canvas buffer");
        } else {
            ESP_LOGE(TAG, "Failed to allocate canvas buffer");
            lv_obj_del(canvas);
            free(qrcode);
            free(temp_buffer);
            return NULL;
        }
    }
    
    // 设置画布缓冲区
    lv_canvas_set_buffer(canvas, canvas_buf, canvas_size, canvas_size, LV_IMG_CF_TRUE_COLOR);
    
    // 填充白色背景
    lv_canvas_fill_bg(canvas, lv_color_white(), LV_OPA_COVER);
    
    // 绘制二维码到画布
    ESP_LOGI(TAG, "Drawing QR code to canvas...");
    
    for (int y = 0; y < qr_size; y++) {
        for (int x = 0; x < qr_size; x++) {
            if (qrcodegen_getModule(qrcode, x, y)) {
                // 绘制黑色模块
                int canvas_x = (x + border) * scale;
                int canvas_y = (y + border) * scale;
                
                // 填充scale x scale的区域
                for (int dy = 0; dy < scale; dy++) {
                    for (int dx = 0; dx < scale; dx++) {
                        int px = canvas_x + dx;
                        int py = canvas_y + dy;
                        
                        if (px < canvas_size && py < canvas_size) {
                            lv_canvas_set_px(canvas, px, py, lv_color_black());
                        }
                    }
                }
            }
        }
    }
    
    // 设置画布位置
    lv_obj_set_pos(canvas, x_pos, y_pos);
    
    // 释放临时缓冲区
    free(qrcode);
    free(temp_buffer);
    
    ESP_LOGI(TAG, "Canvas QR code created successfully");
    
    return canvas;
}

/**
 * @brief 生成并显示支付宝二维码（画布安全版本）
 * @param parent 父对象
 * @return 画布对象指针或NULL
 */
lv_obj_t* qrcode_show_alipay_canvas_safe(lv_obj_t* parent)
{
    if (!parent || !lv_obj_is_valid(parent)) {
        ESP_LOGE(TAG, "Parent object is NULL or invalid");
        return NULL;
    }
    
    // 计算居中位置
    lv_coord_t x_pos = QRCODE_CENTER_X(160);  // 画布约160像素
    lv_coord_t y_pos = QRCODE_CENTER_Y(160);
    
    ESP_LOGI(TAG, "Creating safe canvas Alipay QR code at (%d, %d)", x_pos, y_pos);
    
    return qrcode_create_canvas_safe(parent, QRCODE_ALIPAY_URL, x_pos, y_pos, 0);
}

/**
 * @brief 清理画布QR码资源
 * @param canvas 画布对象
 */
void qrcode_canvas_safe_cleanup(lv_obj_t* canvas)
{
    if (!canvas || !lv_obj_is_valid(canvas)) {
        return;
    }
    
    // 获取画布缓冲区
    lv_img_dsc_t* img_dsc = lv_canvas_get_img(canvas);
    if (img_dsc && img_dsc->data) {
        free((void*)img_dsc->data);
        ESP_LOGI(TAG, "Canvas buffer freed");
    }
    
    // 删除画布对象
    lv_obj_del(canvas);
    ESP_LOGI(TAG, "Canvas object deleted");
}
