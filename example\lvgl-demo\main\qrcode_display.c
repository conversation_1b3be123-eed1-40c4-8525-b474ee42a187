/**
 * @file qrcode_display.c
 * @brief LVGL二维码显示功能实现
 * <AUTHOR> Agent
 * @date 2025-08-01
 * 
 * 基于qrcodegen库实现在ESP32-S3显示屏上绘制二维码的功能
 * 适配240x320分辨率显示屏和LVGL 8.3.1
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "lvgl.h"
#include "qrcodegen.h"

static const char *TAG = "QRCODE_DISPLAY";

// 支付宝二维码URL
#define ALIPAY_QR_URL "https://render.alipay.hk/p/s/hkwallet/landing/qrcode?code=281004040598XXwl31cYpzUaA50b9ga0dFkQ"

// 显示屏参数 (基于ZX2D80CE02S-2432配置)
#define SCREEN_WIDTH  240
#define SCREEN_HEIGHT 320

// 二维码显示参数
#define QR_BORDER_SIZE    4    // 二维码边框大小
#define QR_MAX_SIZE      200   // 二维码最大显示尺寸
#define QR_MIN_SCALE      2    // 最小缩放比例
#define QR_MAX_SCALE      8    // 最大缩放比例

/**
 * @brief 计算适合屏幕的二维码缩放比例
 * @param qr_size 二维码模块数量
 * @param max_display_size 最大显示尺寸
 * @return 计算得出的缩放比例
 */
static int calculate_qr_scale(int qr_size, int max_display_size)
{
    if (qr_size <= 0) {
        ESP_LOGE(TAG, "Invalid QR code size: %d", qr_size);
        return QR_MIN_SCALE;
    }
    
    // 计算包含边框的总尺寸需要的缩放比例
    int total_modules = qr_size + (QR_BORDER_SIZE * 2);
    int scale = max_display_size / total_modules;
    
    // 限制缩放比例范围
    if (scale < QR_MIN_SCALE) {
        scale = QR_MIN_SCALE;
    } else if (scale > QR_MAX_SCALE) {
        scale = QR_MAX_SCALE;
    }
    
    ESP_LOGI(TAG, "QR size: %d modules, calculated scale: %d", qr_size, scale);
    return scale;
}

/**
 * @brief 在LVGL画布上绘制二维码
 * @param parent 父对象
 * @param text 要编码的文本
 * @param x_pos X坐标
 * @param y_pos Y坐标  
 * @param scale 缩放比例
 * @return 画布对象指针或NULL
 */
lv_obj_t* qrcode_create_canvas(lv_obj_t* parent, const char* text, lv_coord_t x_pos, lv_coord_t y_pos, int scale)
{
    if (!parent || !text || strlen(text) == 0) {
        ESP_LOGE(TAG, "Invalid parameters");
        return NULL;
    }
    
    ESP_LOGI(TAG, "Creating QR code for: %.50s%s", text, strlen(text) > 50 ? "..." : "");
    
    // 分配QR码生成所需的缓冲区
    uint8_t *qrcode = heap_caps_malloc(qrcodegen_BUFFER_LEN_MAX, MALLOC_CAP_8BIT);
    uint8_t *temp_buffer = heap_caps_malloc(qrcodegen_BUFFER_LEN_MAX, MALLOC_CAP_8BIT);
    
    if (!qrcode || !temp_buffer) {
        ESP_LOGE(TAG, "Failed to allocate memory for QR code generation");
        if (qrcode) free(qrcode);
        if (temp_buffer) free(temp_buffer);
        return NULL;
    }
    
    // 清零缓冲区
    memset(qrcode, 0, qrcodegen_BUFFER_LEN_MAX);
    memset(temp_buffer, 0, qrcodegen_BUFFER_LEN_MAX);
    
    // 生成二维码
    bool success = qrcodegen_encodeText(
        text,
        temp_buffer,
        qrcode,
        qrcodegen_Ecc_LOW,           // 低错误纠正级别，节省空间
        qrcodegen_VERSION_MIN,       // 最小版本
        qrcodegen_VERSION_MAX,       // 最大版本
        qrcodegen_Mask_AUTO,         // 自动选择掩码
        true                         // 提升错误纠正级别
    );
    
    if (!success) {
        ESP_LOGE(TAG, "Failed to generate QR code");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }
    
    // 获取二维码尺寸
    int qr_size = qrcodegen_getSize(qrcode);
    ESP_LOGI(TAG, "Generated QR code size: %d x %d", qr_size, qr_size);
    
    // 如果没有指定缩放比例，自动计算
    if (scale <= 0) {
        scale = calculate_qr_scale(qr_size, QR_MAX_SIZE);
    }
    
    // 计算画布尺寸（包含边框）
    int canvas_size = (qr_size + QR_BORDER_SIZE * 2) * scale;
    
    // 检查画布尺寸是否超出屏幕
    if (canvas_size > SCREEN_WIDTH || canvas_size > SCREEN_HEIGHT) {
        ESP_LOGW(TAG, "QR code size (%d) may exceed screen dimensions", canvas_size);
    }
    
    // 创建画布对象
    lv_obj_t *canvas = lv_canvas_create(parent);
    if (!canvas) {
        ESP_LOGE(TAG, "Failed to create canvas object");
        free(qrcode);
        free(temp_buffer);
        return NULL;
    }
    
    // 计算画布缓冲区大小 (RGB565格式，每像素2字节)
    size_t canvas_buf_size = canvas_size * canvas_size * sizeof(lv_color_t);
    
    // 分配画布缓冲区 (优先使用PSRAM)
    lv_color_t *canvas_buf = NULL;

    // 首先尝试PSRAM
    canvas_buf = heap_caps_malloc(canvas_buf_size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (canvas_buf) {
        ESP_LOGI(TAG, "Using PSRAM for canvas buffer (%zu bytes)", canvas_buf_size);
    } else {
        // PSRAM分配失败，尝试使用内部RAM
        canvas_buf = heap_caps_malloc(canvas_buf_size, MALLOC_CAP_8BIT);
        if (canvas_buf) {
            ESP_LOGW(TAG, "Using internal RAM for canvas buffer (%zu bytes)", canvas_buf_size);
        } else {
            ESP_LOGE(TAG, "Failed to allocate canvas buffer (%zu bytes)", canvas_buf_size);
            lv_obj_del(canvas);
            free(qrcode);
            free(temp_buffer);
            return NULL;
        }
    }
    
    // 设置画布缓冲区
    lv_canvas_set_buffer(canvas, canvas_buf, canvas_size, canvas_size, LV_IMG_CF_TRUE_COLOR);
    
    // 填充白色背景
    lv_canvas_fill_bg(canvas, lv_color_white(), LV_OPA_COVER);
    
    // 绘制二维码
    ESP_LOGI(TAG, "Drawing QR code with scale %d, canvas size %d x %d", scale, canvas_size, canvas_size);
    
    for (int y = -QR_BORDER_SIZE; y < qr_size + QR_BORDER_SIZE; y++) {
        for (int x = -QR_BORDER_SIZE; x < qr_size + QR_BORDER_SIZE; x++) {
            // 获取模块状态（边框区域为白色）
            bool is_dark = (x >= 0 && x < qr_size && y >= 0 && y < qr_size) ? 
                          qrcodegen_getModule(qrcode, x, y) : false;
            
            // 计算画布上的像素位置
            int canvas_x = (x + QR_BORDER_SIZE) * scale;
            int canvas_y = (y + QR_BORDER_SIZE) * scale;
            
            // 绘制缩放后的模块（填充scale x scale的矩形）
            lv_color_t color = is_dark ? lv_color_black() : lv_color_white();
            
            for (int dy = 0; dy < scale; dy++) {
                for (int dx = 0; dx < scale; dx++) {
                    int px = canvas_x + dx;
                    int py = canvas_y + dy;
                    
                    // 边界检查
                    if (px >= 0 && px < canvas_size && py >= 0 && py < canvas_size) {
                        lv_canvas_set_px(canvas, px, py, color);
                    }
                }
            }
        }
    }
    
    // 设置画布位置
    lv_obj_set_pos(canvas, x_pos, y_pos);
    
    // 释放临时缓冲区
    free(qrcode);
    free(temp_buffer);
    
    ESP_LOGI(TAG, "QR code canvas created successfully at (%d, %d)", x_pos, y_pos);
    
    // 注意：canvas_buf不能释放，因为画布对象需要持续使用它
    // 当画布对象被删除时，需要手动释放这个缓冲区
    
    return canvas;
}

/**
 * @brief 生成并显示支付宝二维码
 * @param parent 父对象
 * @return 画布对象指针或NULL
 */
lv_obj_t* qrcode_show_alipay(lv_obj_t* parent)
{
    if (!parent) {
        ESP_LOGE(TAG, "Parent object is NULL");
        return NULL;
    }
    
    // 计算居中位置
    lv_coord_t x_pos = (SCREEN_WIDTH - QR_MAX_SIZE) / 2;
    lv_coord_t y_pos = (SCREEN_HEIGHT - QR_MAX_SIZE) / 2;
    
    ESP_LOGI(TAG, "Creating Alipay QR code at center position (%d, %d)", x_pos, y_pos);
    
    return qrcode_create_canvas(parent, ALIPAY_QR_URL, x_pos, y_pos, 0);
}

/**
 * @brief 释放二维码画布资源
 * @param canvas 画布对象
 */
void qrcode_canvas_cleanup(lv_obj_t* canvas)
{
    if (!canvas) {
        return;
    }
    
    // 获取画布缓冲区指针
    lv_img_dsc_t* img_dsc = lv_canvas_get_img(canvas);
    if (img_dsc && img_dsc->data) {
        // 释放画布缓冲区
        free((void*)img_dsc->data);
        ESP_LOGI(TAG, "Canvas buffer freed");
    }
    
    // 删除画布对象
    lv_obj_del(canvas);
    ESP_LOGI(TAG, "Canvas object deleted");
}
