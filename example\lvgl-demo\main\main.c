#include <stdio.h>
#include "string.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "qmsd_board.h"
#include "qmsd_utils.h"
#include "lv_demo_widgets.h"
#include "qrcodegen.h"

#define TAG "QMSD-MAIN"

#ifdef CONFIG_QMSD_GUI_LVGL_V7
#error "example not support lvgl 7 now"
#endif

// 前向声明
extern lv_obj_t* qrcode_show_alipay(lv_obj_t* parent);
extern lv_obj_t* qrcode_show_alipay_simple(lv_obj_t* parent);
extern void qrcode_test_generation(void);

// QR码显示相关变量
static lv_obj_t* qr_canvas = NULL;
static bool show_qr_code = false;

/**
 * @brief 按钮事件处理函数
 */
static void btn_qr_event_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED) {
        ESP_LOGI(TAG, "QR code button clicked");

        if (!show_qr_code) {
            // 显示二维码（使用简化版本）
            qr_canvas = qrcode_show_alipay_simple(lv_scr_act());
            if (qr_canvas) {
                show_qr_code = true;
                ESP_LOGI(TAG, "QR code displayed successfully");
            } else {
                ESP_LOGE(TAG, "Failed to display QR code");
            }
        } else {
            // 隐藏二维码
            if (qr_canvas) {
                lv_obj_del(qr_canvas);
                qr_canvas = NULL;
                show_qr_code = false;
                ESP_LOGI(TAG, "QR code hidden");
            }
        }
    }
}

/**
 * @brief 定时器回调函数，用于延迟创建QR码按钮或直接显示QR码
 */
static void qr_button_timer_cb(lv_timer_t* timer)
{
    // 检查当前屏幕上是否有widgets demo的内容
    lv_obj_t* screen = lv_scr_act();
    uint32_t child_count = lv_obj_get_child_cnt(screen);

    if (child_count > 1) {
        // 有多个子对象，可能是widgets demo，创建按钮
        create_qr_button();
    } else {
        // 简单背景，先测试QR码生成，然后显示
        ESP_LOGI(TAG, "Testing QR code generation first");
        qrcode_test_generation();

        ESP_LOGI(TAG, "Displaying QR code directly");
        qr_canvas = qrcode_show_alipay_simple(lv_scr_act());
        if (qr_canvas) {
            show_qr_code = true;
            ESP_LOGI(TAG, "QR code displayed successfully");

            // 创建一个切换按钮
            lv_obj_t* btn = lv_btn_create(lv_scr_act());
            lv_obj_set_size(btn, 100, 35);
            lv_obj_align(btn, LV_ALIGN_BOTTOM_MID, 0, -20);
            lv_obj_add_event_cb(btn, btn_qr_event_handler, LV_EVENT_ALL, NULL);

            lv_obj_t* label = lv_label_create(btn);
            lv_label_set_text(label, "Hide QR");
            lv_obj_center(label);
        } else {
            ESP_LOGE(TAG, "Failed to display QR code");
        }
    }

    lv_timer_del(timer);
}

/**
 * @brief 创建QR码显示按钮
 */
static void create_qr_button(void)
{
    // 创建按钮
    lv_obj_t* btn = lv_btn_create(lv_scr_act());
    lv_obj_set_size(btn, 120, 40);
    lv_obj_align(btn, LV_ALIGN_TOP_RIGHT, -10, 10);
    lv_obj_add_event_cb(btn, btn_qr_event_handler, LV_EVENT_ALL, NULL);

    // 创建按钮标签
    lv_obj_t* label = lv_label_create(btn);
    lv_label_set_text(label, "Show QR");
    lv_obj_center(label);

    ESP_LOGI(TAG, "QR code button created");
}

void gui_user_init()
{
    // 选择启动模式：0=原始demo, 1=QR码测试
    int startup_mode = 1;

    if (startup_mode == 0) {
        // 启动原始的widgets demo
        lv_demo_widgets();
        // 延迟创建QR码按钮
        lv_timer_create(qr_button_timer_cb, 1000, NULL);
    } else {
        // 直接显示QR码测试
        ESP_LOGI(TAG, "Starting QR code test mode");

        // 创建一个简单的背景
        lv_obj_t* bg = lv_obj_create(lv_scr_act());
        lv_obj_set_size(bg, LV_PCT(100), LV_PCT(100));
        lv_obj_set_style_bg_color(bg, lv_color_white(), 0);
        lv_obj_clear_flag(bg, LV_OBJ_FLAG_SCROLLABLE);

        // 创建标题
        lv_obj_t* title = lv_label_create(bg);
        lv_label_set_text(title, "QR Code Test");
        lv_obj_set_style_text_font(title, &lv_font_montserrat_16, 0);
        lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 20);

        // 延迟显示QR码，给系统一些初始化时间
        lv_timer_create(qr_button_timer_cb, 500, NULL);
    }
}

void app_main(void)
{   
    gpio_install_isr_service(ESP_INTR_FLAG_SHARED);
    qmsd_board_config_t config = QMSD_BOARD_DEFAULT_CONFIG;
    config.gui.refresh_task.core = 1;
    qmsd_board_init(&config);
    printf("Fine qmsd!\r\n");
}
